/**
 * Consolidated Xcode Utility Tools
 * Merges functionality from Xcode-specific utilities and integrations
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { SecureCommandExecutor } from "../services/command-service.js";

/**
 * Register all Xcode utility tools (9 tools)
 */
export function registerXcodeTools(server: XcodeServer) {
  // 1. run_xcrun
  server.server.tool(
    "run_xcrun",
    "Executes a specified Xcode tool via xcrun",
    {
      tool: z.string().describe("The name of the Xcode tool to run"),
      args: z.string().optional().describe("Arguments to pass to the tool"),
      workingDir: z
        .string()
        .optional()
        .describe("Working directory to execute the command in"),
    },
    async ({ tool, args, workingDir }) => {
      try {
        const xcrunArgs = [tool];

        if (args) {
          // Simple argument parsing - split by spaces but preserve quoted strings
          const parsedArgs = args.match(/(?:[^\s"]+|"[^"]*")+/g) || [];
          xcrunArgs.push(
            ...parsedArgs.map((arg) => arg.replace(/^"(.*)"$/, "$1"))
          );
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcrun",
          xcrunArgs,
          {
            timeout: 120000, // 2 minutes
            cwd: workingDir || server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `xcrun ${tool} completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `xcrun ${tool} failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. compile_asset_catalog
  server.server.tool(
    "compile_asset_catalog",
    "Compiles an asset catalog (.xcassets) using actool",
    {
      catalogPath: z
        .string()
        .describe("Path to the asset catalog (.xcassets directory)"),
      outputDir: z
        .string()
        .describe("Directory where compiled assets should be placed"),
      platform: z
        .enum(["iphoneos", "iphonesimulator", "macosx", "watchos"])
        .optional()
        .describe("Target platform (default: iphoneos)"),
      minDeploymentTarget: z
        .string()
        .optional()
        .describe("Minimum deployment target version (default: 14.0)"),
      targetDevices: z
        .array(z.string())
        .optional()
        .describe("Target devices (default: ['iphone', 'ipad'])"),
      appIcon: z
        .string()
        .optional()
        .describe("Name of the app icon set to include"),
    },
    async ({
      catalogPath,
      outputDir,
      platform = "iphoneos",
      minDeploymentTarget = "14.0",
      targetDevices = ["iphone", "ipad"],
      appIcon,
    }) => {
      try {
        const args = [
          "actool",
          "--output-format",
          "human-readable-text",
          "--notices",
          "--warnings",
          "--platform",
          platform,
          "--minimum-deployment-target",
          minDeploymentTarget,
          "--compile",
          outputDir,
        ];

        targetDevices.forEach((device) => {
          args.push("--target-device", device);
        });

        if (appIcon) {
          args.push("--app-icon", appIcon);
        }

        args.push(catalogPath);

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcrun",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Asset catalog compiled successfully!\nCatalog: ${catalogPath}\nOutput: ${outputDir}\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Asset catalog compilation failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. run_lldb
  server.server.tool(
    "run_lldb",
    "Launches the LLDB debugger with optional arguments",
    {
      args: z.string().optional().describe("Arguments to pass to lldb"),
      command: z.string().optional().describe("Single LLDB command to execute"),
    },
    async ({ args, command }) => {
      try {
        const lldbArgs = [];

        if (command) {
          lldbArgs.push("-o", command);
        }

        if (args) {
          const parsedArgs = args.match(/(?:[^\s"]+|"[^"]*")+/g) || [];
          lldbArgs.push(
            ...parsedArgs.map((arg) => arg.replace(/^"(.*)"$/, "$1"))
          );
        }

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcrun",
          ["lldb", ...lldbArgs],
          {
            timeout: 60000, // 1 minute
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `LLDB session completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `LLDB failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. trace_app
  server.server.tool(
    "trace_app",
    "Captures a performance trace of an application using xctrace",
    {
      appPath: z.string().describe("Path to the application to trace"),
      template: z
        .string()
        .optional()
        .describe("Trace template to use (default: 'Time Profiler')"),
      duration: z
        .number()
        .optional()
        .describe("Duration of the trace in seconds (default: 10)"),
      outputPath: z
        .string()
        .optional()
        .describe(
          "Path where to save the trace file (default: app_trace.trace in active directory)"
        ),
      startSuspended: z
        .boolean()
        .optional()
        .describe("Start the application in a suspended state"),
    },
    async ({
      appPath,
      template = "Time Profiler",
      duration = 10,
      outputPath,
      startSuspended = false,
    }) => {
      try {
        const traceOutput = outputPath || `app_trace_${Date.now()}.trace`;

        const args = [
          "xctrace",
          "record",
          "--template",
          template,
          "--time-limit",
          `${duration}s`,
          "--output",
          traceOutput,
        ];

        if (startSuspended) {
          args.push("--launch-suspended");
        }

        args.push("--launch", appPath);

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcrun",
          args,
          {
            timeout: (duration + 30) * 1000, // Duration + 30 seconds buffer
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Performance trace completed!\nApp: ${appPath}\nTemplate: ${template}\nDuration: ${duration}s\nOutput: ${traceOutput}\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Performance tracing failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. get_xcode_info
  server.server.tool(
    "get_xcode_info",
    "Get information about Xcode installations on the system",
    {},
    async () => {
      try {
        // Get active developer directory
        const { stdout: devDir } = await SecureCommandExecutor.execute(
          "xcode-select",
          ["-p"],
          {
            timeout: 10000,
          }
        );

        // Get Xcode version
        const { stdout: version } = await SecureCommandExecutor.execute(
          "xcrun",
          ["xcodebuild", "-version"],
          {
            timeout: 10000,
          }
        );

        // Get available SDKs
        const { stdout: sdks } = await SecureCommandExecutor.execute(
          "xcrun",
          ["xcodebuild", "-showsdks"],
          {
            timeout: 30000,
          }
        );

        let result = `Xcode Information:\n\n`;
        result += `Active Developer Directory:\n${devDir.trim()}\n\n`;
        result += `Version Information:\n${version.trim()}\n\n`;
        result += `Available SDKs:\n${sdks.trim()}`;

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get Xcode info: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. switch_xcode
  server.server.tool(
    "switch_xcode",
    "Switch the active Xcode version",
    {
      xcodePath: z
        .string()
        .optional()
        .describe(
          "Path to the Xcode.app to use. If not provided, available Xcode installations will be listed."
        ),
      version: z
        .string()
        .optional()
        .describe(
          "Version of Xcode to use (e.g., '14.3'). Will use the first matching version found."
        ),
    },
    async ({ xcodePath, version }) => {
      try {
        if (!xcodePath && !version) {
          // List available Xcode installations
          const { stdout } = await SecureCommandExecutor.execute(
            "find",
            ["/Applications", "-name", "Xcode*.app", "-maxdepth", "2"],
            {
              timeout: 30000,
            }
          );

          const xcodeApps = stdout.trim().split("\n").filter(Boolean);

          let result = `Available Xcode installations:\n\n`;
          for (const app of xcodeApps) {
            try {
              const { stdout: versionInfo } =
                await SecureCommandExecutor.execute(
                  "xcrun",
                  [
                    "--developer-dir",
                    `${app}/Contents/Developer`,
                    "xcodebuild",
                    "-version",
                  ],
                  { timeout: 10000 }
                );

              result += `${app}\n  ${versionInfo.split("\n")[0]}\n\n`;
            } catch {
              result += `${app}\n  (Version info unavailable)\n\n`;
            }
          }

          return {
            content: [
              {
                type: "text" as const,
                text: result,
              },
            ],
          };
        }

        let targetPath = xcodePath;

        if (version && !xcodePath) {
          // Find Xcode by version
          const { stdout } = await SecureCommandExecutor.execute(
            "find",
            ["/Applications", "-name", "Xcode*.app", "-maxdepth", "2"],
            { timeout: 30000 }
          );

          const xcodeApps = stdout.trim().split("\n").filter(Boolean);

          for (const app of xcodeApps) {
            try {
              const { stdout: versionInfo } =
                await SecureCommandExecutor.execute(
                  "xcrun",
                  [
                    "--developer-dir",
                    `${app}/Contents/Developer`,
                    "xcodebuild",
                    "-version",
                  ],
                  { timeout: 10000 }
                );

              if (versionInfo.includes(version)) {
                targetPath = app;
                break;
              }
            } catch {
              continue;
            }
          }

          if (!targetPath) {
            throw new Error(
              `No Xcode installation found matching version '${version}'`
            );
          }
        }

        if (!targetPath) {
          throw new Error("No Xcode path specified");
        }

        // Switch to the specified Xcode
        const developerDir = `${targetPath}/Contents/Developer`;

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "sudo",
          ["xcode-select", "-s", developerDir],
          {
            timeout: 30000,
          }
        );

        // Verify the switch
        const { stdout: currentDir } = await SecureCommandExecutor.execute(
          "xcode-select",
          ["-p"],
          {
            timeout: 10000,
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Xcode switched successfully!\nNew active developer directory: ${currentDir.trim()}\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to switch Xcode: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 7. validate_app
  server.server.tool(
    "validate_app",
    "Validate an app for App Store submission using altool",
    {
      ipaPath: z.string().describe("Path to the .ipa file to validate"),
      username: z
        .string()
        .describe("App Store Connect username (usually an email)"),
      password: z
        .string()
        .describe("App-specific password for the App Store Connect account"),
      apiKey: z
        .string()
        .optional()
        .describe("API Key ID (alternative to username/password)"),
      apiKeyPath: z
        .string()
        .optional()
        .describe("Path to the API Key .p8 file (required if using apiKey)"),
      apiIssuer: z
        .string()
        .optional()
        .describe("API Key Issuer ID (required if using apiKey)"),
    },
    async ({ ipaPath, username, password, apiKey, apiKeyPath, apiIssuer }) => {
      try {
        const args = ["altool", "--validate-app"];

        if (apiKey && apiKeyPath && apiIssuer) {
          args.push("--apiKey", apiKey);
          args.push("--apiKeyPath", apiKeyPath);
          args.push("--apiIssuer", apiIssuer);
        } else {
          args.push("--username", username);
          args.push("--password", password);
        }

        args.push("--file", ipaPath);
        args.push("--type", "ios");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcrun",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `App validation completed!\nIPA: ${ipaPath}\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `App validation failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 8. generate_icon_set
  server.server.tool(
    "generate_icon_set",
    "Generate an app icon set from a source image",
    {
      sourceImage: z
        .string()
        .describe("Path to the source image (should be at least 1024x1024)"),
      outputPath: z
        .string()
        .describe("Path where to create the AppIcon.appiconset directory"),
      platform: z
        .enum(["ios", "macos", "watchos"])
        .optional()
        .describe("Target platform (default: ios)"),
    },
    async ({ sourceImage, outputPath, platform = "ios" }) => {
      try {
        // This is a simplified implementation
        // In a full implementation, this would use sips or ImageMagick to generate different sizes

        const iconSetPath = `${outputPath}/AppIcon.appiconset`;

        // Create the icon set directory
        await SecureCommandExecutor.execute("mkdir", ["-p", iconSetPath], {
          timeout: 10000,
        });

        // Copy source image as the largest size
        await SecureCommandExecutor.execute(
          "cp",
          [sourceImage, `${iconSetPath}/icon-1024.png`],
          {
            timeout: 30000,
          }
        );

        // Create a basic Contents.json
        const contentsJson = {
          images: [
            {
              filename: "icon-1024.png",
              idiom: "universal",
              platform: platform,
              size: "1024x1024",
            },
          ],
          info: {
            author: "xcode-mcp-server",
            version: 1,
          },
        };

        const fs = await import("fs/promises");
        await fs.writeFile(
          `${iconSetPath}/Contents.json`,
          JSON.stringify(contentsJson, null, 2)
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Icon set generated successfully!\nSource: ${sourceImage}\nOutput: ${iconSetPath}\nPlatform: ${platform}\n\nNote: This is a basic implementation. For production use, consider using proper icon generation tools.`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Icon set generation failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Note: This completes 8 Xcode tools. The 9th tool would be added here if needed.
}
