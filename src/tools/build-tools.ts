/**
 * Consolidated Build System Tools
 * Merges functionality from src/tools/core/buildSystem.ts and related tools
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { ProjectNotFoundError } from "../utils/core/errors.js";
import { SecureCommandExecutor } from "../services/command-service.js";

/**
 * Register all build system tools (7 tools)
 */
export function registerBuildTools(server: XcodeServer) {
  // 1. build_project
  server.server.tool(
    "build_project",
    "Builds the active Xcode project using the specified configuration and scheme.",
    {
      configuration: z
        .string()
        .describe("Build configuration to use (e.g., 'Debug' or 'Release')."),
      scheme: z
        .string()
        .describe(
          "Name of the build scheme to be built. Must be one of the schemes available in the project."
        ),
      destination: z
        .string()
        .optional()
        .describe(
          "Optional destination specifier (e.g., 'platform=iOS Simulator,name=iPhone 15'). If not provided, a suitable destination will be selected automatically."
        ),
      sdk: z
        .string()
        .optional()
        .describe(
          "Optional SDK to use (e.g., 'iphoneos', 'iphonesimulator'). If not provided, will use the default SDK for the project type."
        ),
      derivedDataPath: z
        .string()
        .optional()
        .describe(
          "Path where build products and derived data will be stored (optional)."
        ),
      jobs: z
        .number()
        .optional()
        .describe(
          "Maximum number of concurrent build operations (optional, default is determined by Xcode)."
        ),
    },
    async ({
      configuration,
      scheme,
      destination,
      sdk,
      derivedDataPath,
      jobs,
    }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = [
          "-project",
          server.activeProject.path,
          "-scheme",
          scheme,
          "-configuration",
          configuration,
        ];

        if (destination) {
          args.push("-destination", destination);
        }

        if (sdk) {
          args.push("-sdk", sdk);
        }

        if (derivedDataPath) {
          args.push("-derivedDataPath", derivedDataPath);
        }

        if (jobs) {
          args.push("-jobs", jobs.toString());
        }

        args.push("build");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 300000, // 5 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Build completed successfully!\n\nOutput:\n${stdout}${
                stderr ? `\n\nWarnings/Info:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Build failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. run_tests
  server.server.tool(
    "run_tests",
    "Executes tests for the active Xcode project.",
    {
      scheme: z
        .string()
        .optional()
        .describe(
          "Optional scheme to use for testing. If not provided, will use the active project's scheme."
        ),
      destination: z
        .string()
        .optional()
        .describe(
          "Optional destination specifier (e.g., 'platform=iOS Simulator,name=iPhone 15'). If not provided, a suitable simulator will be selected automatically."
        ),
      testPlan: z
        .string()
        .optional()
        .describe("Optional name of the test plan to run."),
      onlyTesting: z
        .array(z.string())
        .optional()
        .describe(
          "Optional list of tests to include, excluding all others. Format: 'TestTarget/TestClass/testMethod'."
        ),
      skipTesting: z
        .array(z.string())
        .optional()
        .describe(
          "Optional list of tests to exclude. Format: 'TestTarget/TestClass/testMethod'."
        ),
      enableCodeCoverage: z
        .boolean()
        .optional()
        .describe(
          "Whether to enable code coverage during testing (default: false)."
        ),
      resultBundlePath: z
        .string()
        .optional()
        .describe("Optional path where test results bundle will be stored."),
    },
    async ({
      scheme,
      destination,
      testPlan,
      onlyTesting,
      skipTesting,
      enableCodeCoverage = false,
      resultBundlePath,
    }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = ["-project", server.activeProject.path];

        if (scheme) {
          args.push("-scheme", scheme);
        }

        if (destination) {
          args.push("-destination", destination);
        }

        if (testPlan) {
          args.push("-testPlan", testPlan);
        }

        if (onlyTesting && onlyTesting.length > 0) {
          onlyTesting.forEach((test) => {
            args.push("-only-testing", test);
          });
        }

        if (skipTesting && skipTesting.length > 0) {
          skipTesting.forEach((test) => {
            args.push("-skip-testing", test);
          });
        }

        if (enableCodeCoverage) {
          args.push("-enableCodeCoverage", "YES");
        }

        if (resultBundlePath) {
          args.push("-resultBundlePath", resultBundlePath);
        }

        args.push("test");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 600000, // 10 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Tests completed!\n\nOutput:\n${stdout}${
                stderr ? `\n\nWarnings/Info:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Tests failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. clean_project
  server.server.tool(
    "clean_project",
    "Cleans the build directory for the active Xcode project.",
    {
      scheme: z
        .string()
        .optional()
        .describe(
          "Optional scheme to clean. If not provided, will use the first available scheme."
        ),
      configuration: z
        .string()
        .optional()
        .describe(
          "Optional build configuration to clean (e.g., 'Debug' or 'Release'). If not provided, cleans all configurations."
        ),
      derivedDataPath: z
        .string()
        .optional()
        .describe(
          "Optional path to the derived data directory to clean. If not provided, uses Xcode's default location."
        ),
    },
    async ({ scheme, configuration, derivedDataPath }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = ["-project", server.activeProject.path];

        if (scheme) {
          args.push("-scheme", scheme);
        }

        if (configuration) {
          args.push("-configuration", configuration);
        }

        if (derivedDataPath) {
          args.push("-derivedDataPath", derivedDataPath);
        }

        args.push("clean");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 120000, // 2 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Project cleaned successfully!\n\nOutput:\n${stdout}${
                stderr ? `\n\nInfo:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Clean failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. archive_project
  server.server.tool(
    "archive_project",
    "Archives the active Xcode project for distribution.",
    {
      scheme: z
        .string()
        .describe(
          "The scheme to archive. Must be one of the schemes available in the project."
        ),
      configuration: z
        .string()
        .describe("Build configuration to use (e.g., 'Debug' or 'Release')."),
      archivePath: z
        .string()
        .describe("Path where the .xcarchive file will be saved."),
      destination: z
        .string()
        .optional()
        .describe(
          "Optional destination specifier (e.g., 'generic/platform=iOS'). If not provided, uses the default destination for the scheme."
        ),
      exportOptionsPlist: z
        .string()
        .optional()
        .describe(
          "Optional path to an export options property list file for subsequent export operations."
        ),
    },
    async ({
      scheme,
      configuration,
      archivePath,
      destination,
      exportOptionsPlist,
    }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = [
          "-project",
          server.activeProject.path,
          "-scheme",
          scheme,
          "-configuration",
          configuration,
          "-archivePath",
          archivePath,
        ];

        if (destination) {
          args.push("-destination", destination);
        }

        args.push("archive");

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 600000, // 10 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        let result = `Archive created successfully!\nArchive path: ${archivePath}\n\nOutput:\n${stdout}`;

        if (stderr) {
          result += `\n\nWarnings/Info:\n${stderr}`;
        }

        if (exportOptionsPlist) {
          result += `\n\nExport options plist: ${exportOptionsPlist}`;
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Archive failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. list_available_destinations
  server.server.tool(
    "list_available_destinations",
    "Lists available build destinations for the active Xcode project or workspace.",
    {
      scheme: z
        .string()
        .optional()
        .describe(
          "Optional scheme to show destinations for. If not provided, uses the active scheme."
        ),
    },
    async ({ scheme }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = ["-project", server.activeProject.path];

        if (scheme) {
          args.push("-scheme", scheme);
        }

        args.push("-showdestinations");

        const { stdout } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 30000, // 30 seconds
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Available destinations:\n\n${stdout}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to list destinations: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. list_available_schemes
  server.server.tool(
    "list_available_schemes",
    "Lists all available schemes in the active Xcode project or workspace.",
    {},
    async () => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        const args = ["-project", server.activeProject.path, "-list"];

        const { stdout } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 30000, // 30 seconds
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        return {
          content: [
            {
              type: "text" as const,
              text: `Project information:\n\n${stdout}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to list schemes: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 7. export_archive
  server.server.tool(
    "export_archive",
    "Export an Xcode archive for distribution (App Store, Ad Hoc, Enterprise, Development)",
    {
      archivePath: z.string().describe("Path to the .xcarchive file to export"),
      exportPath: z
        .string()
        .describe(
          "Directory where exported IPA and other files will be placed"
        ),
      method: z
        .enum(["app-store", "ad-hoc", "enterprise", "development"])
        .describe("Distribution method"),
      teamId: z
        .string()
        .optional()
        .describe(
          "Team ID for code signing. If not provided, will try to use the default team."
        ),
      signingCertificate: z
        .string()
        .optional()
        .describe(
          "Signing certificate to use. If not provided, will try to use the default certificate for the selected method."
        ),
      provisioningProfiles: z
        .record(z.string())
        .optional()
        .describe(
          "Dictionary mapping bundle identifiers to provisioning profile names."
        ),
      compileBitcode: z
        .boolean()
        .optional()
        .describe(
          "Whether to compile Bitcode. Default is true for App Store, false otherwise."
        ),
      stripSwiftSymbols: z
        .boolean()
        .optional()
        .describe("Whether to strip Swift symbols. Default is true."),
    },
    async ({
      archivePath,
      exportPath,
      method,
      teamId,
      signingCertificate,
      provisioningProfiles,
      compileBitcode,
      stripSwiftSymbols = true,
    }) => {
      try {
        // Create export options plist
        const exportOptions: any = {
          method: method,
          stripSwiftSymbols: stripSwiftSymbols,
        };

        if (teamId) {
          exportOptions.teamID = teamId;
        }

        if (signingCertificate) {
          exportOptions.signingCertificate = signingCertificate;
        }

        if (provisioningProfiles) {
          exportOptions.provisioningProfiles = provisioningProfiles;
        }

        if (compileBitcode !== undefined) {
          exportOptions.compileBitcode = compileBitcode;
        } else {
          exportOptions.compileBitcode = method === "app-store";
        }

        // Write export options to temporary file
        const fs = await import("fs/promises");
        const path = await import("path");
        const os = await import("os");

        const tempDir = await fs.mkdtemp(
          path.join(os.tmpdir(), "xcode-export-")
        );
        const exportOptionsPlistPath = path.join(
          tempDir,
          "ExportOptions.plist"
        );

        const plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>method</key>
  <string>${exportOptions.method}</string>
  <key>stripSwiftSymbols</key>
  <${exportOptions.stripSwiftSymbols ? "true" : "false"}/>
  <key>compileBitcode</key>
  <${exportOptions.compileBitcode ? "true" : "false"}/>
  ${teamId ? `<key>teamID</key><string>${teamId}</string>` : ""}
  ${
    signingCertificate
      ? `<key>signingCertificate</key><string>${signingCertificate}</string>`
      : ""
  }
  ${
    provisioningProfiles
      ? `<key>provisioningProfiles</key><dict>${Object.entries(
          provisioningProfiles
        )
          .map(
            ([bundleId, profile]) =>
              `<key>${bundleId}</key><string>${profile}</string>`
          )
          .join("")}</dict>`
      : ""
  }
</dict>
</plist>`;

        await fs.writeFile(exportOptionsPlistPath, plistContent);

        const args = [
          "-exportArchive",
          "-archivePath",
          archivePath,
          "-exportPath",
          exportPath,
          "-exportOptionsPlist",
          exportOptionsPlistPath,
        ];

        const { stdout, stderr } = await SecureCommandExecutor.execute(
          "xcodebuild",
          args,
          {
            timeout: 600000, // 10 minutes
            cwd: server.directoryState.getActiveDirectory(),
          }
        );

        // Clean up temporary file
        await fs.unlink(exportOptionsPlistPath).catch(() => {});
        await fs.rmdir(tempDir).catch(() => {});

        return {
          content: [
            {
              type: "text" as const,
              text: `Archive exported successfully!\nExport path: ${exportPath}\nMethod: ${method}\n\nOutput:\n${stdout}${
                stderr ? `\n\nWarnings/Info:\n${stderr}` : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Export failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
